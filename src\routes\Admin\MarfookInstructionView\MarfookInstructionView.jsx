import {
  Box,
  <PERSON>ton,
  Card,
  CardContent,
  Chip,
  CircularProgress,
  Divider,
  Grid,
  IconButton,
  Typography,
} from '@mui/material';
import { useQuery, useMutation } from 'react-query';
import { useParams, useNavigate } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { Edit, ArrowBack, Delete } from '@mui/icons-material';
import {
  getMarfookInstruction,
  deleteMarfookInstruction,
} from '../../../apis/marfook';
import LoadingPage from '../../../components/LoadingPage/LoadingPage';
import { setSnackbar } from '../../../store/layout';
import { PATHS } from '../../../constants';

export default function MarfookInstructionView() {
  const { instructionId } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const { isLoading, data, error } = useQuery(
    [PATHS.admin.marfookInstructions, instructionId],
    () => getMarfookInstruction(instructionId),
    {
      enabled: !!instructionId,
    },
  );

  if (isLoading) {
    return <LoadingPage />;
  }

  if (error) {
    return (
      <Box sx={{ textAlign: 'center', mt: 4 }}>
        <Typography variant="h6" color="error">
          خطا در بارگذاری اعلان
        </Typography>
        <Button
          variant="contained"
          onClick={() => navigate(PATHS.admin.marfookInstructions)}
          sx={{ mt: 2 }}
        >
          بازگشت به لیست
        </Button>
      </Box>
    );
  }

  const instruction = data?.data;

  if (!instruction) {
    return (
      <Box sx={{ textAlign: 'center', mt: 4 }}>
        <Typography variant="h6">اعلان یافت نشد</Typography>
        <Button
          variant="contained"
          onClick={() => navigate(PATHS.admin.marfookInstructions)}
          sx={{ mt: 2 }}
        >
          بازگشت به لیست
        </Button>
      </Box>
    );
  }

  const handleEdit = () => {
    navigate(`${PATHS.admin.marfookInstructions}/edit/${instructionId}`, {
      state: { instruction },
    });
  };

  const handleBack = () => {
    navigate(PATHS.admin.marfookInstructions);
  };

  const deleteMutation = useMutation(
    () => deleteMarfookInstruction(instructionId),
    {
      onSuccess: () => {
        dispatch(
          setSnackbar({
            message: 'اعلان مرفوک با موفقیت حذف شد',
            severity: 'success',
          }),
        );
        navigate(PATHS.admin.marfookInstructions);
      },
      onError: () => {
        dispatch(
          setSnackbar({
            message: 'خطا در حذف اعلان مرفوک',
            severity: 'error',
          }),
        );
      },
    },
  );

  const handleDelete = () => {
    if (window.confirm('آیا از حذف این اعلان اطمینان دارید؟')) {
      deleteMutation.mutate();
    }
  };

  return (
    <Box sx={{ width: '100%', height: '100%', overflowY: 'scroll', p: 2 }}>
      {/* Header */}
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          mb: 3,
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <IconButton onClick={handleBack} sx={{ mr: 2 }}>
            <ArrowBack />
          </IconButton>
          <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
            نمایش اعلان مرفوک
          </Typography>
        </Box>
        <Button
          variant="contained"
          startIcon={<Edit />}
          onClick={handleEdit}
          sx={{ ml: 2 }}
        >
          ویرایش
        </Button>
      </Box>

      <Grid container spacing={3}>
        <Grid item xs={12} lg={8}>
          {/* Main Content */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2 }}>
                {instruction.title}
              </Typography>

              <Divider sx={{ my: 2 }} />

              <Typography variant="body1" sx={{ mb: 3, lineHeight: 1.8 }}>
                {instruction.description}
              </Typography>

              <Divider sx={{ my: 3 }} />

              {/* Subjects Section */}
              <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2 }}>
                محورها
              </Typography>

              {instruction.subject && instruction.subject.length > 0 ? (
                instruction.subject.map((subj, index) => (
                  <Card
                    key={subj.id}
                    sx={{ mb: 2, backgroundColor: '#f8f9fa' }}
                  >
                    <CardContent>
                      <Typography
                        variant="subtitle1"
                        sx={{ fontWeight: 'bold', mb: 1 }}
                      >
                        {index + 1}. {subj.title}
                      </Typography>

                      {subj.description && (
                        <Typography
                          variant="body2"
                          sx={{ mb: 2, color: 'text.secondary' }}
                        >
                          {subj.description}
                        </Typography>
                      )}

                      {subj.hashtags && subj.hashtags.length > 0 && (
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                          {subj.hashtags.map((hashtag, hashtagIndex) => (
                            <Chip
                              key={hashtagIndex}
                              label={hashtag}
                              size="small"
                              color="primary"
                              variant="outlined"
                            />
                          ))}
                        </Box>
                      )}
                    </CardContent>
                  </Card>
                ))
              ) : (
                <Typography variant="body2" color="text.secondary">
                  هیچ محوری تعریف نشده است
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} lg={4}>
          {/* Sidebar */}
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2 }}>
                اطلاعات اعلان
              </Typography>

              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" color="text.secondary">
                  تاریخ ایجاد:
                </Typography>
                <Typography variant="body1">
                  {instruction.created_at
                    ? new Date(instruction.created_at).toLocaleDateString(
                        'fa-IR',
                      )
                    : 'نامشخص'}
                </Typography>
              </Box>

              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" color="text.secondary">
                  تاریخ انقضا:
                </Typography>
                <Typography variant="body1">
                  {instruction.expiration_date
                    ? new Date(instruction.expiration_date).toLocaleDateString(
                        'fa-IR',
                      )
                    : 'نامشخص'}
                </Typography>
              </Box>

              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" color="text.secondary">
                  تعداد محورها:
                </Typography>
                <Typography variant="body1">
                  {instruction.subject ? instruction.subject.length : 0}
                </Typography>
              </Box>

              <Divider sx={{ my: 2 }} />

              <Button
                variant="outlined"
                color="error"
                startIcon={<Delete />}
                fullWidth
                sx={{ mt: 2 }}
                onClick={handleDelete}
                disabled={deleteMutation.isLoading}
              >
                {deleteMutation.isLoading ? (
                  <CircularProgress size={20} />
                ) : (
                  'حذف اعلان'
                )}
              </Button>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
}
